from flask import Flask, render_template, request, redirect, url_for
import sqlite3

app = Flask(__name__)

# Initialize database
def init_db():
    conn = sqlite3.connect('database.db')
    c = conn.cursor()
    c.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            price REAL NOT NULL,
            quantity INTEGER NOT NULL
        )
    ''')
    c.execute('''
        CREATE TABLE IF NOT EXISTS sales (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            product_id INTEGER,
            quantity INTEGER,
            sale_date TEXT
        )
    ''')
    conn.commit()
    conn.close()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/products')
def products():
    conn = sqlite3.connect('database.db')
    c = conn.cursor()
    c.execute('SELECT * FROM products')
    products = c.fetchall()
    conn.close()
    return render_template('products.html', products=products)

@app.route('/add_product', methods=['GET', 'POST'])
def add_product():
    if request.method == 'POST':
        name = request.form['name']
        price = float(request.form['price'])
        quantity = int(request.form['quantity'])

        conn = sqlite3.connect('database.db')
        c = conn.cursor()
        c.execute('INSERT INTO products (name, price, quantity) VALUES (?, ?, ?)', (name, price, quantity))
        conn.commit()
        conn.close()
        return redirect(url_for('products'))
    return render_template('add_product.html')

@app.route('/sales', methods=['GET', 'POST'])
def sales():
    conn = sqlite3.connect('database.db')
    c = conn.cursor()

    if request.method == 'POST':
        product_id = int(request.form['product_id'])
        quantity = int(request.form['quantity'])
        sale_date = request.form['sale_date']

        c.execute('INSERT INTO sales (product_id, quantity, sale_date) VALUES (?, ?, ?)', (product_id, quantity, sale_date))

        # Update product quantity
        c.execute('UPDATE products SET quantity = quantity - ? WHERE id = ?', (quantity, product_id))

        conn.commit()
    c.execute('SELECT * FROM sales')
    sales = c.fetchall()
    conn.close()
    return render_template('sales.html', sales=sales)

if __name__ == '__main__':
    init_db()
    app.run(debug=True)
